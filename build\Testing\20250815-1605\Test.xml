<?xml version="1.0" encoding="UTF-8"?>
<Site BuildName="(empty)"
	BuildStamp="20250815-1605-Experimental"
	Name="(empty)"
	Generator="ctest-4.1.0"
	CompilerName=""
	CompilerVersion=""
	OSName="Windows"
	Hostname="Huawei"
	OSRelease=" Professional"
	OSVersion=" (Build 22631)"
	OSPlatform="AMD64"
	Is64Bits="1"
	VendorString="GenuineIntel"
	VendorID="Intel Corporation"
	FamilyID="6"
	ModelID="10"
	ModelName=""
	ProcessorCacheSize="-1"
	NumberOfLogicalCPU="20"
	NumberOfPhysicalCPU="14"
	TotalVirtualMemory="36110"
	TotalPhysicalMemory="16110"
	LogicalProcessorsPerPhysical="1"
	ProcessorClockFrequency="2300"
	>
	<Testing>
		<StartDateTime>Aug 16 00:05 [NON-UTF-8-BYTE-0xD6]й[NON-UTF-8-BYTE-0xFA][NON-UTF-8-BYTE-0xB1][NON-UTF-8-BYTE-0xEA]׼ʱ[NON-UTF-8-BYTE-0xBC][NON-UTF-8-BYTE-0xE4]</StartDateTime>
		<StartTestTime>1755273929</StartTestTime>
		<TestList/>
		<EndDateTime>Aug 16 00:05 [NON-UTF-8-BYTE-0xD6]й[NON-UTF-8-BYTE-0xFA][NON-UTF-8-BYTE-0xB1][NON-UTF-8-BYTE-0xEA]׼ʱ[NON-UTF-8-BYTE-0xBC][NON-UTF-8-BYTE-0xE4]</EndDateTime>
		<EndTestTime>1755273929</EndTestTime>
		<ElapsedMinutes>0</ElapsedMinutes>
	</Testing>
</Site>
